{"apis": [{"id": "ergonomic-assessment-v1", "name": "Ergonomic Assessment API v1", "url": "https://api.example.com/v1/ergonomic-assessment", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer YOUR_API_KEY"}, "description": "Primary ergonomic assessment API", "category": "Ergonomics"}, {"id": "posture-analysis-v2", "name": "Posture Analysis API v2", "url": "https://api.example.com/v2/posture-analysis", "method": "POST", "headers": {"Content-Type": "application/json", "X-API-Key": "YOUR_API_KEY"}, "description": "Advanced posture analysis with annotations", "category": "Posture"}, {"id": "workspace-detector", "name": "Workspace Detection API", "url": "https://api.example.com/workspace/detect", "method": "POST", "headers": {"Content-Type": "application/json"}, "description": "Detects and analyzes workspace elements", "category": "Workspace"}, {"id": "sitting-posture", "name": "Sitting Posture Analysis", "url": "https://aiec2.synergopro.com/api/v1/posture/sitting-posture", "method": "POST", "headers": {"Content-Type": "application/json"}, "description": "Analyzes sitting posture for ergonomic assessment", "category": "Posture"}, {"id": "sitting-height", "name": "Sitting Height Analysis", "url": "https://aiec2.synergopro.com/api/v1/posture/sitting-height", "method": "POST", "headers": {"Content-Type": "application/json"}, "description": "Analyzes sitting height for ergonomic assessment", "category": "Posture"}, {"id": "desk-height", "name": "Desk Height Analysis", "url": "https://aiec2.synergopro.com/api/v1/posture/desk-height", "method": "POST", "headers": {"Content-Type": "application/json"}, "description": "Analyzes desk height for ergonomic assessment", "category": "Posture"}]}