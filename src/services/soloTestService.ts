
import type { APIEndpoint, SoloTestResult } from '@/types/api';

export class SoloTestService {
  static async executeTest(api: APIEndpoint, imageUrl: string): Promise<SoloTestResult> {
    const startTime = Date.now();

    try {
      // Determine the correct field name based on the API endpoint
      const imageFieldName = this.getImageFieldName(api.url);

      const payload = {
        [imageFieldName]: imageUrl,
        timestamp: new Date().toISOString()
      };

      const response = await fetch(api.url, {
        method: api.method,
        headers: api.headers,
        body: JSON.stringify(payload)
      });

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      return {
        success: true,
        data,
        annotatedImageUrl: data.annotated_image_url || data.annotatedImageUrl,
        timestamp: new Date().toISOString(),
        responseTime
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString(),
        responseTime: Date.now() - startTime
      };
    }
  }

  /**
   * Determines the correct field name for the image based on the API endpoint
   * APIs at aiec2.synergopro.com expect 'image' field
   * Other APIs might expect 'image_url' field
   */
  private static getImageFieldName(apiUrl: string): string {
    // Check if this is one of the aiec2.synergopro.com APIs
    if (apiUrl.includes('aiec2.synergopro.com')) {
      return 'image';
    }

    // Default to image_url for other APIs
    return 'image_url';
  }

  static formatResultsForTable(result: SoloTestResult): Array<{key: string, value: any}> {
    if (!result.success || !result.data) {
      return [
        { key: 'Status', value: 'Failed' },
        { key: 'Error', value: result.error || 'Unknown error' }
      ];
    }

    const formatValue = (value: any): string => {
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value, null, 2);
      }
      return String(value);
    };

    const flatten = (obj: any, prefix = ''): Array<{key: string, value: any}> => {
      const items: Array<{key: string, value: any}> = [];

      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;

        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          items.push(...flatten(value, fullKey));
        } else {
          items.push({ key: fullKey, value: formatValue(value) });
        }
      }

      return items;
    };

    return flatten(result.data);
  }
}
