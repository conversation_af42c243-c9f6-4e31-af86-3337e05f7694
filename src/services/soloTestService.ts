
import type { APIEndpoint, SoloTestResult } from '@/types/api';

export class SoloTestService {
  static async executeTest(api: APIEndpoint, imageUrl: string): Promise<SoloTestResult> {
    const startTime = Date.now();

    try {
      // Check if this is one of the aiec2.synergopro.com APIs that need multipart/form-data
      const isAiecAPI = api.url.includes('aiec2.synergopro.com');

      let response: Response;

      if (isAiecAPI) {
        // For aiec2.synergopro.com APIs, we need to handle image files differently
        response = await this.makeAiecAPIRequest(api, imageUrl);
      } else {
        // For other APIs, use the original JSON approach
        const imageFieldName = this.getImageFieldName(api.url);
        const payload = {
          [imageFieldName]: imageUrl,
          timestamp: new Date().toISOString()
        };

        response = await fetch(api.url, {
          method: api.method,
          headers: api.headers,
          body: JSON.stringify(payload)
        });
      }

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      return {
        success: true,
        data,
        annotatedImageUrl: data.annotated_image_url || data.annotatedImageUrl || data.annotated_image,
        timestamp: new Date().toISOString(),
        responseTime
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString(),
        responseTime: Date.now() - startTime
      };
    }
  }

  /**
   * Makes a request to aiec2.synergopro.com APIs using multipart/form-data
   */
  private static async makeAiecAPIRequest(api: APIEndpoint, imageUrl: string): Promise<Response> {
    // Convert image URL to blob for form data
    let imageBlob: Blob;

    if (imageUrl.startsWith('data:')) {
      // Handle data URLs (base64 encoded images)
      const response = await fetch(imageUrl);
      imageBlob = await response.blob();
    } else {
      // Handle regular URLs - fetch the image
      const imageResponse = await fetch(imageUrl);
      if (!imageResponse.ok) {
        throw new Error(`Failed to fetch image from URL: ${imageResponse.status}`);
      }
      imageBlob = await imageResponse.blob();
    }

    // Create form data
    const formData = new FormData();
    formData.append('image', imageBlob, 'image.jpg');
    formData.append('include_image', 'true');

    // Make request without Content-Type header (let browser set it for multipart)
    const headers = { ...api.headers };
    delete headers['Content-Type']; // Remove Content-Type to let browser set it correctly

    return fetch(api.url, {
      method: api.method,
      headers,
      body: formData
    });
  }

  /**
   * Determines the correct field name for the image based on the API endpoint
   * APIs at aiec2.synergopro.com expect 'image' field
   * Other APIs might expect 'image_url' field
   */
  private static getImageFieldName(apiUrl: string): string {
    // Check if this is one of the aiec2.synergopro.com APIs
    if (apiUrl.includes('aiec2.synergopro.com')) {
      return 'image';
    }

    // Default to image_url for other APIs
    return 'image_url';
  }

  static formatResultsForTable(result: SoloTestResult): Array<{key: string, value: any}> {
    if (!result.success || !result.data) {
      return [
        { key: 'Status', value: 'Failed' },
        { key: 'Error', value: result.error || 'Unknown error' }
      ];
    }

    const formatValue = (value: any): string => {
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value, null, 2);
      }
      return String(value);
    };

    const flatten = (obj: any, prefix = ''): Array<{key: string, value: any}> => {
      const items: Array<{key: string, value: any}> = [];

      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;

        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          items.push(...flatten(value, fullKey));
        } else {
          items.push({ key: fullKey, value: formatValue(value) });
        }
      }

      return items;
    };

    return flatten(result.data);
  }
}
